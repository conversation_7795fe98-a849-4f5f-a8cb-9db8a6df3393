using System;
using System.Security.Cryptography;
using System.Text;
using OtpNet;

// Simple test to verify Base32 encoding/decoding and TOTP generation
class Program
{
    static void Main()
    {
        Console.WriteLine("Testing Base32 encoding and TOTP generation...");
        
        // Generate a test secret
        var key = new byte[20]; // 160 bits
        using (var rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(key);
        }
        
        // Encode to Base32
        string base32Secret = Base32Encode(key);
        Console.WriteLine($"Generated Base32 secret: {base32Secret}");
        
        // Decode back to bytes
        byte[] decodedKey = Base32Decode(base32Secret);
        
        // Verify they match
        bool matches = true;
        for (int i = 0; i < key.Length; i++)
        {
            if (key[i] != decodedKey[i])
            {
                matches = false;
                break;
            }
        }
        
        Console.WriteLine($"Encoding/Decoding test: {(matches ? "PASSED" : "FAILED")}");
        
        // Test TOTP generation
        var totp = new Totp(key);
        string totpCode = totp.ComputeTotp();
        Console.WriteLine($"Generated TOTP code: {totpCode}");
        
        // Test TOTP validation
        bool isValid = totp.VerifyTotp(totpCode, out long timeStepMatched);
        Console.WriteLine($"TOTP validation test: {(isValid ? "PASSED" : "FAILED")}");
        
        // Generate QR code URL
        string qrCodeUrl = $"otpauth://totp/DotnetAuth:<EMAIL>?secret={base32Secret}&issuer=DotnetAuth&algorithm=SHA1&digits=6&period=30";
        Console.WriteLine($"QR Code URL: {qrCodeUrl}");
        
        Console.WriteLine("\nAll tests completed!");
    }
    
    private static string Base32Encode(byte[] data)
    {
        const string base32Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";
        var result = new StringBuilder();
        
        for (int i = 0; i < data.Length; i += 5)
        {
            var chunk = new byte[5];
            var chunkLength = Math.Min(5, data.Length - i);
            Array.Copy(data, i, chunk, 0, chunkLength);
            
            // Convert 5 bytes (40 bits) to 8 base32 characters
            ulong value = 0;
            for (int j = 0; j < chunkLength; j++)
            {
                value = (value << 8) | chunk[j];
            }
            
            // Pad with zeros if needed
            value <<= (5 - chunkLength) * 8;
            
            // Extract 5-bit groups and convert to base32
            var chars = new char[8];
            for (int j = 7; j >= 0; j--)
            {
                chars[j] = base32Chars[(int)(value & 0x1F)];
                value >>= 5;
            }
            
            // Add only the needed characters (no padding for our use case)
            var neededChars = (chunkLength * 8 + 4) / 5;
            result.Append(chars, 8 - neededChars, neededChars);
        }
        
        return result.ToString();
    }

    private static byte[] Base32Decode(string base32)
    {
        const string base32Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";
        var result = new List<byte>();
        
        // Remove any whitespace and convert to uppercase
        base32 = base32.Replace(" ", "").Replace("-", "").ToUpper();
        
        for (int i = 0; i < base32.Length; i += 8)
        {
            var chunk = base32.Substring(i, Math.Min(8, base32.Length - i));
            ulong value = 0;
            
            foreach (char c in chunk)
            {
                var index = base32Chars.IndexOf(c);
                if (index < 0) throw new ArgumentException($"Invalid Base32 character: {c}");
                value = (value << 5) | (uint)index;
            }
            
            // Extract bytes from the accumulated value
            var byteCount = (chunk.Length * 5) / 8;
            for (int j = byteCount - 1; j >= 0; j--)
            {
                result.Add((byte)((value >> (j * 8)) & 0xFF));
            }
        }
        
        return result.ToArray();
    }
}
